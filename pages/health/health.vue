<template>
	<view class="container">
		<view class="decoration"></view>
		
		<view class="header">
			<navigator url="/pages/index/index" class="back-button">←</navigator>
			<text class="header-title">健康知识</text>
		</view>

		<view class="main-content">
			<view class="category-tabs">
				<view 
					class="category-tab" 
					:class="{ active: activeCategory === item.value }"
					v-for="(item, index) in categories" 
					:key="index"
					@tap="switchCategory(item.value)"
				>
					{{ item.label }}
				</view>
			</view>

			<scroll-view 
				v-if="articleList.length > 0" 
				class="article-scroll" 
				:scroll-y="true" 
				:scroll-top="scrollTop"
				@scrolltolower="loadMore"
			>
				<view class="article-card" v-for="(article, index) in articleList" :key="index">
					<view class="article-title">{{ article.title }}</view>
					<view class="article-meta">
						<view class="article-author">
							<image class="author-avatar" :src="article.authorAvatar || '/static/images/noportait.png'"></image>
							<text>{{ article.author }}</text>
						</view>
						<text>{{ article.publishTime }}</text>
					</view>
					<text class="article-content">{{ article.content }}</text>
					<view class="article-actions">
						<view class="action-buttons">
							<view class="action-button" @tap="likeArticle(article, index)">
								<text>👍</text>
								<text>{{ article.likeCount || 0 }}</text>
							</view>
							<view class="action-button" @tap="commentArticle(article)">
								<text>💬</text>
								<text>{{ article.commentCount || 0 }}</text>
							</view>
						</view>
						<view class="read-count">
							<text>阅读 {{ formatReadCount(article.readCount) }}</text>
						</view>
					</view>
				</view>
				
				<view class="load-more" v-if="hasMore">
					<text>{{ loadingText }}</text>
				</view>
			</scroll-view>
			
			<tui-show-empty v-else text="暂无健康知识文章"></tui-show-empty>
		</view>

		<view class="nav">
			<navigator url="/pages/index/index" class="nav-item">首页</navigator>
			<navigator url="/pages/appoint/appoint" class="nav-item">预约</navigator>
			<navigator url="/pages/health/health" class="nav-item active">健康</navigator>
			<navigator url="/pages/topic/topic" class="nav-item">圈子</navigator>
			<navigator url="/pages/ucenter/index/index" class="nav-item">我的</navigator>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				activeCategory: 'all',
				categories: [
					{ label: '全部', value: 'all' },
					{ label: '养生保健', value: 'health' },
					{ label: '中医理论', value: 'tcm' },
					{ label: '食疗药膳', value: 'diet' },
					{ label: '经络穴位', value: 'acupoint' },
					{ label: '常见疾病', value: 'disease' }
				],
				articleList: [],
				page: 1,
				size: 10,
				hasMore: true,
				scrollTop: 0,
				loadingText: '加载中...'
			}
		},
		methods: {
			// 切换分类
			switchCategory(category) {
				this.activeCategory = category;
				this.page = 1;
				this.hasMore = true;
				this.articleList = [];
				this.getHealthArticles();
			},
			
			// 获取健康知识文章
			getHealthArticles() {
				let that = this;
				
				// 显示加载提示
				if (that.page === 1) {
					uni.showLoading({
						title: '加载中...'
					});
				}
				
				// 这里使用模拟数据，实际项目中应该调用真实API
				// util.request(api.HealthArticles, {
				// 	page: that.page,
				// 	size: that.size,
				// 	category: that.activeCategory
				// }).then(function(res) {
				// 	if (res.errno === 0) {
				// 		if (that.page === 1) {
				// 			that.articleList = res.data.datalist;
				// 		} else {
				// 			that.articleList = that.articleList.concat(res.data.datalist);
				// 		}
				// 		that.hasMore = res.data.hasMore;
				// 	}
				// 	uni.hideLoading();
				// });
				
				// 模拟数据
				setTimeout(() => {
					const mockData = that.getMockArticles();
					if (that.page === 1) {
						that.articleList = mockData;
					} else {
						that.articleList = that.articleList.concat(mockData);
					}
					that.hasMore = that.page < 3; // 模拟只有3页数据
					uni.hideLoading();
				}, 1000);
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore) {
					this.page++;
					this.loadingText = '加载更多...';
					this.getHealthArticles();
				}
			},
			
			// 点赞文章
			likeArticle(article, index) {
				// 实际项目中应该调用API
				article.likeCount = (article.likeCount || 0) + 1;
				this.$set(this.articleList, index, article);
			},
			
			// 评论文章
			commentArticle(article) {
				// 跳转到评论页面
				uni.navigateTo({
					url: `/pages/comment/comment?articleId=${article.id}`
				});
			},
			
			// 格式化阅读数
			formatReadCount(count) {
				if (count >= 1000) {
					return (count / 1000).toFixed(1) + 'k';
				}
				return count;
			},
			
			// 获取模拟数据
			getMockArticles() {
				return [
					{
						id: 1,
						title: '春季养生之道：顺应自然，调养肝气',
						author: '张医生',
						authorAvatar: '/static/images/noportait.png',
						publishTime: '2024-03-15',
						content: '春季养生重在"养肝"，肝主疏泄，喜条达而恶抑郁。春季万物复苏，阳气生发，此时养生应当顺应自然，调养肝气。本文将从起居、饮食、运动等方面详细介绍春季养生要点...',
						likeCount: 128,
						commentCount: 36,
						readCount: 2300
					},
					{
						id: 2,
						title: '中医体质辨识与调理指南',
						author: '李医生',
						authorAvatar: '/static/images/noportait.png',
						publishTime: '2024-03-14',
						content: '中医将人体体质分为九种基本类型，包括平和质、气虚质、阳虚质、阴虚质、痰湿质、湿热质、血瘀质、气郁质和特禀质。了解自己的体质类型，才能更好地进行养生保健...',
						likeCount: 96,
						commentCount: 24,
						readCount: 1800
					}
				];
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.page = 1;
			this.hasMore = true;
			this.articleList = [];
			this.getHealthArticles();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function(options) {
			this.getHealthArticles();
		}
	}
</script>

<style lang="scss">
	:root {
		--primary-color: #8B4513;
		--secondary-color: #D2B48C;
		--background-color: #FDF5E6;
		--text-color: #4A4A4A;
		--accent-color: #CD853F;
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	.container {
		max-width: 750rpx;
		margin: 0 auto;
		background: #fff;
		min-height: 100vh;
		position: relative;
	}

	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.05;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/></svg>');
		background-size: 100rpx 100rpx;
	}

	.header {
		background: var(--primary-color);
		color: #fff;
		padding: 30rpx;
		text-align: center;
		position: relative;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/></svg>');
		background-size: 40rpx 40rpx;
		border-bottom: 4rpx solid var(--secondary-color);
	}

	.header-title {
		font-size: 48rpx;
		letter-spacing: 8rpx;
		text-shadow: 4rpx 4rpx 8rpx rgba(0,0,0,0.2);
	}

	.back-button {
		position: absolute;
		left: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #fff;
		text-decoration: none;
		font-size: 36rpx;
	}

	.main-content {
		padding: 40rpx;
		padding-bottom: 120rpx;
	}

	.category-tabs {
		display: flex;
		gap: 32rpx;
		padding: 36rpx 20rpx 20rpx 20rpx;
		background: linear-gradient(90deg, #f9f6f2 80%, #f3e9d7 100%);
		border-radius: 36rpx;
		margin: 0 20rpx 36rpx 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(139,69,19,0.04);
		overflow-x: auto;
		white-space: nowrap;
	}

	.category-tab {
		min-width: 128rpx;
		padding: 16rpx 44rpx;
		border-radius: 44rpx;
		border: 3rpx solid var(--secondary-color);
		background: #fff;
		color: var(--primary-color);
		font-size: 32rpx;
		font-weight: 500;
		margin-right: 8rpx;
		transition: all 0.2s;
		box-shadow: 0 2rpx 8rpx rgba(139,69,19,0.03);
		cursor: pointer;
		position: relative;
		flex-shrink: 0;
	}

	.category-tab:last-child {
		margin-right: 0;
	}

	.category-tab.active {
		background: linear-gradient(90deg, #8B4513 80%, #D2B48C 100%);
		color: #fff;
		border: none;
		font-weight: bold;
		box-shadow: 0 4rpx 16rpx rgba(139,69,19,0.10);
	}

	.article-scroll {
		height: calc(100vh - 400rpx);
	}

	.article-card {
		background: #fff;
		border: 4rpx solid var(--secondary-color);
		border-radius: 16rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
		position: relative;
		overflow: hidden;
	}

	.article-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 8rpx;
		background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
	}

	.article-title {
		font-size: 36rpx;
		color: var(--primary-color);
		margin-bottom: 20rpx;
		font-weight: bold;
		position: relative;
		padding-left: 30rpx;
	}

	.article-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		background: var(--primary-color);
		border-radius: 4rpx;
	}

	.article-meta {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		color: #666;
		font-size: 28rpx;
	}

	.article-author {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
	}

	.author-avatar {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		margin-right: 10rpx;
		border: 2rpx solid var(--secondary-color);
	}

	.article-content {
		color: var(--text-color);
		margin-bottom: 30rpx;
		line-height: 1.8;
		font-size: 30rpx;
	}

	.article-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 2rpx solid var(--secondary-color);
		padding-top: 20rpx;
	}

	.action-buttons {
		display: flex;
		gap: 30rpx;
	}

	.action-button {
		display: flex;
		align-items: center;
		gap: 10rpx;
		color: var(--text-color);
		font-size: 28rpx;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		transition: all 0.3s ease;
	}

	.action-button:active {
		background: var(--background-color);
	}

	.read-count {
		color: #666;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		gap: 10rpx;
	}

	.load-more {
		text-align: center;
		padding: 20rpx;
		color: #666;
		font-size: 28rpx;
	}

	.nav {
		position: fixed;
		bottom: 0;
		width: 100%;
		max-width: 750rpx;
		background: #fff;
		display: flex;
		justify-content: space-around;
		padding: 20rpx;
		border-top: 4rpx solid var(--secondary-color);
		box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
	}

	.nav-item {
		text-align: center;
		color: var(--text-color);
		text-decoration: none;
		font-size: 24rpx;
		position: relative;
		padding: 10rpx 0;
	}

	.nav-item.active {
		color: var(--primary-color);
	}

	.nav-item.active::after {
		content: '';
		position: absolute;
		bottom: -20rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background: var(--primary-color);
	}
</style>
