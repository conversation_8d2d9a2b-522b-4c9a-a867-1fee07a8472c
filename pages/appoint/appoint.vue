<template>
	<view class="container">
		<!-- 装饰背景 -->
		<view class="decoration"></view>
		
		<!-- 头部 -->
		<view class="header">
			<view class="back-button" @click="goBack">←</view>
			<text class="header-title">预约咨询</text>
		</view>

		<!-- 表单容器 -->
		<view class="form-container">
			<form @submit="submitForm">
				<!-- 姓名 -->
				<view class="form-group">
					<text class="form-label">姓名</text>
					<input 
						class="form-control" 
						type="text" 
						placeholder="请输入您的姓名"
						v-model="formData.name"
						maxlength="20"
					/>
				</view>

				<!-- 手机号码 -->
				<view class="form-group">
					<text class="form-label">手机号码</text>
					<input 
						class="form-control" 
						type="number" 
						placeholder="请输入您的手机号码"
						v-model="formData.phone"
						maxlength="11"
					/>
				</view>

				<!-- 推荐人验证码 -->
				<view class="form-group">
					<text class="form-label">推荐人验证码</text>
					<input 
						class="form-control" 
						type="text" 
						placeholder="请输入推荐人验证码"
						v-model="formData.referralCode"
						maxlength="10"
					/>
				</view>

				<!-- 日期选择器 -->
				<view class="date-picker">
					<view class="date-picker-header">
						<text class="date-picker-title">选择日期</text>
						<view class="date-picker-nav">
							<!-- 上周按钮 - 只在非当前周时显示 -->
							<button 
								v-if="currentWeek > 0"
								class="nav-btn" 
								type="button" 
								@click="prevWeek"
							>上周</button>
							<button 
								class="nav-btn" 
								type="button" 
								@click="nextWeek"
							>下周</button>
						</view>
					</view>
					<view class="date-grid">
						<view 
							class="date-cell" 
							:class="{
								'selected': selectedDate === date.fullDate,
								'disabled': date.disabled
							}"
							v-for="(date, index) in dateList" 
							:key="index"
							@click="selectDate(date)"
						>
							<text class="day">{{date.day}}</text>
							<text class="date">{{date.date}}</text>
						</view>
					</view>
				</view>

				<!-- 时间选择 -->
				<view class="form-group">
					<text class="form-label">预约时间</text>
					<view class="time-slots">
						<view 
							class="time-slot" 
							:class="{
								'selected': selectedTime === slot.time,
								'disabled': slot.disabled
							}"
							v-for="(slot, index) in timeSlots" 
							:key="index"
							@click="selectTime(slot)"
						>
							<text class="time">{{slot.time}}</text>
							<text class="remaining-slots">{{slot.remaining}}</text>
						</view>
					</view>
				</view>

				<!-- 咨询需求 -->
				<view class="form-group">
					<text class="form-label">咨询需求</text>
					<textarea 
						class="form-control textarea" 
						placeholder="请详细描述您的咨询需求..."
						v-model="formData.requirement"
						maxlength="500"
					></textarea>
				</view>

				<!-- 提交按钮 -->
				<button 
					class="submit-btn" 
					type="submit"
					:disabled="!isFormValid"
				>
					提交预约
				</button>
			</form>
		</view>
	</view>
</template>

<script>
	const api = require('@/utils/api.js');
	const util = require("@/utils/util.js")
	
	export default {
		data() {
			return {
				formData: {
					name: '',
					phone: '',
					referralCode: '',
					requirement: ''
				},
				selectedDate: '',
				selectedTime: '',
				dateList: [],
				timeSlots: [
					{ time: '09:00', remaining: '剩余3个名额', disabled: false },
					{ time: '10:00', remaining: '剩余2个名额', disabled: false },
					{ time: '11:00', remaining: '剩余1个名额', disabled: false },
					{ time: '14:00', remaining: '剩余4个名额', disabled: false },
					{ time: '15:00', remaining: '剩余3个名额', disabled: false },
					{ time: '16:00', remaining: '已约满', disabled: true }
				],
				currentWeek: 0
			}
		},
		computed: {
			isFormValid() {
				return this.formData.name && 
					   this.formData.phone && 
					   this.selectedDate && 
					   this.selectedTime;
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			
			// 生成日期列表
			generateDateList(weekOffset = 0) {
				const dateList = [];
				const today = new Date();
				const startDate = new Date(today);
				startDate.setDate(today.getDate() + (weekOffset * 7));
				
				// 调整到本周一
				const dayOfWeek = startDate.getDay();
				const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
				startDate.setDate(startDate.getDate() + mondayOffset);
				
				const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
				
				for (let i = 0; i < 7; i++) {
					const date = new Date(startDate);
					date.setDate(startDate.getDate() + i);
					
					const isDisabled = date.getDay() === 0 || date.getDay() === 6; // 周六周日禁用
					const isToday = this.isSameDay(date, today);
					
					dateList.push({
						day: weekDays[i],
						date: date.getDate(),
						fullDate: this.formatDate(date),
						disabled: isDisabled,
						isToday: isToday
					});
				}
				
				return dateList;
			},
			
			// 判断是否为同一天
			isSameDay(date1, date2) {
				return date1.getFullYear() === date2.getFullYear() &&
					   date1.getMonth() === date2.getMonth() &&
					   date1.getDate() === date2.getDate();
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			// 选择日期
			selectDate(date) {
				if (date.disabled) return;
				this.selectedDate = date.fullDate;
			},
			
			// 选择时间
			selectTime(slot) {
				if (slot.disabled) return;
				this.selectedTime = slot.time;
			},
			
			// 上一周
			prevWeek() {
				if (this.currentWeek > 0) {
					this.currentWeek--;
					this.dateList = this.generateDateList(this.currentWeek);
					this.selectedDate = ''; // 清空已选日期
				}
			},
			
			// 下一周
			nextWeek() {
				this.currentWeek++;
				this.dateList = this.generateDateList(this.currentWeek);
				this.selectedDate = ''; // 清空已选日期
			},
			
			// 提交表单
			submitForm() {
				if (!this.isFormValid) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					});
					return;
				}
				
				// 验证手机号
				if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 构建提交数据
				const submitData = {
					...this.formData,
					appointmentDate: this.selectedDate,
					appointmentTime: this.selectedTime
				};
				
				// 显示加载提示
				uni.showLoading({
					title: '提交中...'
				});
				
				// 模拟API调用
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '预约成功',
						icon: 'success'
					});
					
					// 重置表单
					this.resetForm();
					
					// 延迟返回上一页
					setTimeout(() => {
						this.goBack();
					}, 1500);
				}, 2000);
			},
			
			// 重置表单
			resetForm() {
				this.formData = {
					name: '',
					phone: '',
					referralCode: '',
					requirement: ''
				};
				this.selectedDate = '';
				this.selectedTime = '';
			}
		},
		onLoad() {
			// 初始化日期列表
			this.dateList = this.generateDateList();
		}
	}
</script>

<style lang="scss" scoped>
	// 定义CSS变量
	:root {
		--primary-color: #8B4513;
		--secondary-color: #D2B48C;
		--background-color: #FDF5E6;
		--text-color: #4A4A4A;
		--accent-color: #CD853F;
	}
	
	.container {
		min-height: 100vh;
		background-color: var(--background-color);
		position: relative;
	}
	
	// 装饰背景
	.decoration {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.05;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
		background-size: 50px 50px;
		z-index: -1;
	}
	
	// 头部样式
	.header {
		background: var(--primary-color);
		color: #fff;
		padding: 30rpx;
		text-align: center;
		position: relative;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
		background-size: 40rpx 40rpx;
		border-bottom: 4rpx solid var(--secondary-color);
		
		.back-button {
			position: absolute;
			left: 30rpx;
			top: 50%;
			transform: translateY(-50%);
			color: #fff;
			font-size: 36rpx;
			line-height: 1;
		}
		
		.header-title {
			font-size: 48rpx;
			letter-spacing: 8rpx;
			text-shadow: 4rpx 4rpx 8rpx rgba(0,0,0,0.2);
			font-weight: bold;
		}
	}
	
	// 表单容器
	.form-container {
		padding: 40rpx;
		background: #fff;
		margin: 20rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	}
	
	// 表单组
	.form-group {
		margin-bottom: 40rpx;
		position: relative;
		
		.form-label {
			display: block;
			margin-bottom: 16rpx;
			color: var(--primary-color);
			font-weight: bold;
			position: relative;
			padding-left: 30rpx;
			font-size: 32rpx;
			
			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 8rpx;
				height: 32rpx;
				background: var(--primary-color);
				border-radius: 4rpx;
			}
		}
		
		.form-control {
			width: 90%;
			padding: 24rpx;
			border: 4rpx solid var(--secondary-color);
			border-radius: 8rpx;
			font-size: 28rpx;
			transition: all 0.3s ease;
			background: #fff;
			
			&:focus {
				outline: none;
				border-color: var(--primary-color);
				box-shadow: 0 0 0 4rpx rgba(139, 69, 19, 0.1);
			}
			
			&.textarea {
				min-height: 200rpx;
				resize: none;
			}
		}
	}
	
	// 时间选择器
	.time-slots {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
		margin-bottom: 40rpx;
		
		.time-slot {
			padding: 20rpx;
			border: 2rpx solid var(--secondary-color);
			border-radius: 8rpx;
			text-align: center;
			transition: all 0.3s ease;
			position: relative;
			
			&:hover {
				background: var(--background-color);
			}
			
			&.selected {
				background: var(--primary-color);
				color: #fff;
				border-color: var(--primary-color);
			}
			
			&.disabled {
				background: #f5f5f5;
				color: #999;
				cursor: not-allowed;
			}
			
			.time {
				display: block;
				font-size: 28rpx;
				font-weight: bold;
				margin-bottom: 8rpx;
			}
			
			.remaining-slots {
				font-size: 24rpx;
				color: #666;
				
				.time-slot.selected & {
					color: #fff;
				}
				
				.time-slot.disabled & {
					color: #999;
				}
			}
		}
	}
	
	// 日期选择器
	.date-picker {
		margin-bottom: 40rpx;
		
		.date-picker-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			
			.date-picker-title {
				font-size: 32rpx;
				color: var(--primary-color);
				font-weight: bold;
			}
			
			.date-picker-nav {
				display: flex;
				gap: 20rpx;
				
				.nav-btn {
					background: none;
					border: 2rpx solid var(--secondary-color);
					border-radius: 8rpx;
					padding: 10rpx 20rpx;
					color: var(--text-color);
					transition: all 0.3s ease;
					font-size: 24rpx;
					
					&:hover {
						background: var(--background-color);
					}
					
					&:active {
						background: var(--secondary-color);
						color: #fff;
					}
				}
			}
		}
		
		.date-grid {
			display: grid;
			grid-template-columns: repeat(7, 1fr);
			gap: 10rpx;
			
			.date-cell {
				aspect-ratio: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border: 2rpx solid var(--secondary-color);
				border-radius: 8rpx;
				transition: all 0.3s ease;
				
				&:hover {
					background: var(--background-color);
				}
				
				&.selected {
					background: var(--primary-color);
					color: #fff;
					border-color: var(--primary-color);
				}
				
				&.disabled {
					background: #f5f5f5;
					color: #999;
					cursor: not-allowed;
				}
				
				.day {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 4rpx;
					
					.date-cell.selected & {
						color: #fff;
					}
					
					.date-cell.disabled & {
						color: #999;
					}
				}
				
				.date {
					font-size: 32rpx;
					font-weight: bold;
				}
			}
		}
	}
	
	// 提交按钮
	.submit-btn {
		width: 100%;
		padding: 30rpx;
		background: var(--primary-color);
		color: #fff;
		border: none;
		border-radius: 8rpx;
		font-size: 32rpx;
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
		font-weight: bold;
		
		&:disabled {
			background: #ccc;
			cursor: not-allowed;
		}
		
		&:not(:disabled):hover {
			background: var(--accent-color);
		}
		
		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
			transform: translateX(-100%);
			transition: transform 0.3s ease;
		}
		
		&:not(:disabled):hover::after {
			transform: translateX(100%);
		}
	}
</style> 