module.exports = {
	
	IndexUrlNewGoods: 'shop/newGoods', //新品首发
	IndexUrlHotGoods: 'shop/hotGoods', //热卖商品
	IndexUrlTopic: 'shop/topic', //专题精选
	IndexUrlBrand: 'shop/brand', //品牌制造商
	IndexUrlCategory: 'shop/category', //首页底部的分类及商品列表
	IndexUrlBanner: 'shop/banner', //首页banner
	IndexUrlChannel: 'shop/channel', //banner下的分类
	
	CatalogList: 'shop/catalog', //分类目录全部分类数据接口
	GoodsClassList: 'shop/goodsClassList', //获取商品分类数据，在goodsclass页面
	CatalogCurrent: 'shop/catalogDetail', //分类目录当前分类数据接口
	GoodsLabelList: 'shop/goodsLabelList', //分类目录当前分类数据接口
	GoodsCount: 'shop/goodsSummary', //统计商品总数
	GoodsRelated: 'shop/relatedGoods', //商品详情页的关联商品（大家都在看）
		
	CartList: 'shop/cart', //获取购物车的数据
	CartChecked: 'shop/cartChecked', // 选择或取消选择商品
	CartAdd: 'shop/addCart', // 添加商品到购物车
	CartUpdate: 'shop/updateChart', // 更新购物车的商品
	CartDelete: 'shop/deleteChart', // 删除购物车的商品
	
	GoodsDetail: 'shop/goodsDetail', //获得商品的详情
	GoodsSelect: 'shop/getGoodByCheckedGoodStand', //根据选中的规格获取商品
	CartGoodsCount: 'shop/cartUserData', // 获取购物车商品件数
	GoodsCategory: 'shop/goodsCategory', //获得分类数据
	GoodsSpecial: 'shop/specialGoods',//获取热门、等特殊商品
	
	BuyAdd: 'shop/buyByGoods', // 直接购买
	CartCheckout: 'shop/getCheckOutData', // 下单前信息确认
	OrderSubmit: 'shop/submitOrder', // 提交订单
	

	
	AddressList: 'shop/getUserAddressData', //收货地址列表
	AddressDetail: 'shop/addressDetail', //收货地址详情
	RegionList: 'shop/regionlist', //获取区域列表
	AddressSave: 'shop/saveAddress', //保存收货地址
	AddressDelete: 'shop/deleteAddress', //删除收货地址
	
	PayPrepayId: 'shop/prePayOrder', //获取微信统一下单prepay_id
	
	OrderQuery: 'shop/queryOrder', //微信查询订单状态
	OrderList: 'shop/orderlist', //订单列表
	OrderDetail: 'shop/orderdetail', //订单详情
	OrderCancel: 'shop/cancelOrder', //取消订单
	OrderConfirm: 'shop/confirmOrder', //确认收货
	
	SearchIndex: 'shop/searchKeys', //搜索页面数据
	SearchClearHistory: 'shop/clearSearchHistory', //清空历史记录
	GoodsList: 'shop/goodlist', //获得商品列表
	
	TopicList: 'shop/topiclist', //专题列表
	TopicDetail: 'shop/topicdetail', //专题详情
	TopicRelated: 'topic/related', //相关专题
	
	BrandList: 'shop/brandlist', //品牌列表
	BrandDetail: 'shop/branddetail', //品牌详情
	CommentPost: 'shop/addComment', //发表评论
	CommentList: 'shop/commentlist', //评论列表
	CommentCount: 'shop/commentcount', //评论总数
	
	CouponList: 'shop/getUserConponData', // 优惠券列表
	GoodsCouponList: 'shop/queryUseConponList', // 商品优惠券列表

	CollectAddOrDelete: 'shop/collectGoods', //添加或取消收藏
	CollectList: 'shop/collectlist', //收藏列表
	
	FeedbackTypeList: 'shop/queryFeedbackType', //查询反馈类型
	FeedbackAdd: 'shop/savefeedback', //添加反馈
	
	HelpTypeList: 'shop/helplist', //查看帮助类型列表
	HelpDetail: 'shop/helpdetail', //帮助详情
	
	BindMobile: 'shop/bindUserMobile', //绑定手机 sms
		
	AuthLoginByWeixin: 'auth/login_by_weixin', //微信登录

	FootprintList: 'footprint/list', //足迹列表
	FootprintDelete: 'footprint/delete', //删除足迹


	SmsCode: 'user/smscode', //发送短信
	Login: 'auth/login', //账号登录
	Code:  'auth/', //静默登录
	Register: 'auth/register', //注册





};
